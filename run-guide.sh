#!/bin/bash

# PPT生成器项目运行指南

echo "=========================================="
echo "PPT自动生成器 - 运行指南"
echo "=========================================="

echo ""
echo "1. 编译项目："
echo "   mvn clean compile"

echo ""
echo "2. 运行项目："
echo "   mvn spring-boot:run"

echo ""
echo "3. 生成演示PPT（代码方式）："
echo "   mvn spring-boot:run -Dspring-boot.run.arguments=demo"

echo ""
echo "4. 访问Web接口："
echo "   健康检查：http://localhost:8080/api/ppt/health"
echo "   生成演示PPT：http://localhost:8080/api/ppt/generate-demo"
echo "   文件列表：http://localhost:8080/api/ppt/files"

echo ""
echo "5. 自定义生成PPT（POST请求）："
echo "   URL: http://localhost:8080/api/ppt/generate"
echo "   Content-Type: application/json"
echo "   请求体示例："
cat << 'EOF'
{
  "title": "销售报告",
  "fileName": "sales-report",
  "tables": [
    {
      "title": "季度销售数据",
      "headers": ["季度", "销售额(万元)", "增长率(%)"],
      "rows": [
        ["Q1", "1250.5", "15.2"],
        ["Q2", "1380.8", "18.7"],
        ["Q3", "1425.3", "12.4"],
        ["Q4", "1598.7", "21.3"]
      ]
    }
  ]
}
EOF

echo ""
echo "6. 下载生成的PPT："
echo "   http://localhost:8080/api/ppt/download/{fileName}"

echo ""
echo "=========================================="
echo "特性说明：" 
echo "• 一键生成PPT，包含预设模板和表格"
echo "• 表格边框自动设置为白色"
echo "• 支持多表格和自定义数据"
echo "• 完整的异常处理机制"
echo "• REST API接口，支持远程调用"
echo "=========================================="