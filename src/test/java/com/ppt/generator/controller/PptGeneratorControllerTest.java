package com.ppt.generator.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ppt.generator.model.PptGenerationRequest;
import com.ppt.generator.model.TableData;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureTestMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * PPT生成控制器集成测试类
 * 
 * <AUTHOR> Generator
 */
@SpringBootTest
@AutoConfigureTestMvc
class PptGeneratorControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void testHealthCheck() throws Exception {
        mockMvc.perform(get("/api/ppt/health"))
                .andExpect(status().isOk())
                .andExpect(content().string("PPT Generator Service is running!"));
    }

    @Test
    void testGenerateDemoPpt() throws Exception {
        mockMvc.perform(get("/api/ppt/generate-demo")
                .param("title", "集成测试PPT")
                .param("fileName", "integration-test"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.fileName").value("integration-test.pptx"))
                .andExpect(jsonPath("$.fileSize").exists())
                .andExpect(jsonPath("$.duration").exists());
    }

    @Test
    void testGeneratePptWithCustomData() throws Exception {
        // 准备测试数据
        TableData tableData = TableData.builder()
                .title("API测试表格")
                .headers(Arrays.asList("项目", "状态", "完成度"))
                .rows(Arrays.asList(
                    Arrays.asList("项目A", "进行中", "75%"),
                    Arrays.asList("项目B", "已完成", "100%"),
                    Arrays.asList("项目C", "计划中", "0%")
                ))
                .build();

        PptGenerationRequest request = PptGenerationRequest.builder()
                .title("API集成测试PPT")
                .fileName("api-test")
                .tables(Arrays.asList(tableData))
                .build();

        String requestBody = objectMapper.writeValueAsString(request);

        mockMvc.perform(post("/api/ppt/generate")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.fileName").value("api-test.pptx"))
                .andExpect(jsonPath("$.message").value("PPT生成成功"));
    }

    @Test
    void testListGeneratedFiles() throws Exception {
        // 先生成一个PPT文件
        testGenerateDemoPpt();

        // 然后查询文件列表
        mockMvc.perform(get("/api/ppt/files"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray());
    }
}