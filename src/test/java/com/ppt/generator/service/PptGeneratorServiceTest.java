package com.ppt.generator.service;

import com.ppt.generator.model.PptGenerationRequest;
import com.ppt.generator.model.PptGenerationResponse;
import com.ppt.generator.model.TableData;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.io.File;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * PPT生成服务测试类
 * 
 * <AUTHOR> Generator
 */
@SpringBootTest
@TestPropertySource(properties = {
    "ppt.generator.output-path=./test-output/",
    "ppt.generator.file-prefix=test-ppt-"
})
class PptGeneratorServiceTest {

    @Autowired
    private PptGeneratorService pptGeneratorService;

    @Test
    void testGeneratePptWithSingleTable() {
        // 准备测试数据
        TableData tableData = TableData.builder()
                .title("测试表格")
                .headers(Arrays.asList("姓名", "年龄", "职位", "部门"))
                .rows(Arrays.asList(
                    Arrays.asList("张三", "28", "工程师", "技术部"),
                    Arrays.asList("李四", "32", "产品经理", "产品部"),
                    Arrays.asList("王五", "26", "设计师", "设计部")
                ))
                .build();

        PptGenerationRequest request = PptGenerationRequest.builder()
                .title("单表格测试PPT")
                .fileName("single-table-test")
                .tables(Arrays.asList(tableData))
                .build();

        // 执行测试
        PptGenerationResponse response = pptGeneratorService.generatePpt(request);

        // 验证结果
        assertTrue(response.isSuccess(), "PPT生成应该成功");
        assertNotNull(response.getFilePath(), "文件路径不应为空");
        assertTrue(response.getFileName().endsWith(".pptx"), "文件名应以.pptx结尾");
        assertTrue(response.getFileSize() > 0, "文件大小应大于0");
        assertTrue(response.getDuration() > 0, "生成耗时应大于0");

        // 验证文件确实存在
        File generatedFile = new File(response.getFilePath());
        assertTrue(generatedFile.exists(), "生成的PPT文件应该存在");
        
        System.out.println("单表格测试PPT生成成功:");
        System.out.println("文件路径: " + response.getFilePath());
        System.out.println("文件大小: " + response.getFileSize() + " 字节");
        System.out.println("生成耗时: " + response.getDuration() + " 毫秒");
    }

    @Test
    void testGeneratePptWithMultipleTables() {
        // 准备第一个表格数据
        TableData salesTable = TableData.builder()
                .title("销售数据统计")
                .headers(Arrays.asList("月份", "销售额", "增长率"))
                .rows(Arrays.asList(
                    Arrays.asList("1月", "150万", "12%"),
                    Arrays.asList("2月", "168万", "15%"),
                    Arrays.asList("3月", "182万", "8%")
                ))
                .build();

        // 准备第二个表格数据
        TableData employeeTable = TableData.builder()
                .title("员工绩效统计")
                .headers(Arrays.asList("员工姓名", "绩效得分", "等级", "奖金"))
                .rows(Arrays.asList(
                    Arrays.asList("员工A", "95", "优秀", "5000"),
                    Arrays.asList("员工B", "88", "良好", "3000"),
                    Arrays.asList("员工C", "76", "合格", "1000")
                ))
                .build();

        PptGenerationRequest request = PptGenerationRequest.builder()
                .title("多表格测试PPT")
                .fileName("multi-table-test")
                .tables(Arrays.asList(salesTable, employeeTable))
                .build();

        // 执行测试
        PptGenerationResponse response = pptGeneratorService.generatePpt(request);

        // 验证结果
        assertTrue(response.isSuccess(), "多表格PPT生成应该成功");
        assertNotNull(response.getFilePath(), "文件路径不应为空");
        
        // 验证文件确实存在
        File generatedFile = new File(response.getFilePath());
        assertTrue(generatedFile.exists(), "生成的PPT文件应该存在");
        
        System.out.println("多表格测试PPT生成成功:");
        System.out.println("文件路径: " + response.getFilePath());
        System.out.println("文件大小: " + response.getFileSize() + " 字节");
        System.out.println("生成耗时: " + response.getDuration() + " 毫秒");
    }

    @Test
    void testGeneratePptWithEmptyData() {
        PptGenerationRequest request = PptGenerationRequest.builder()
                .title("空数据测试PPT")
                .fileName("empty-test")
                .tables(Arrays.asList())
                .build();

        // 执行测试
        PptGenerationResponse response = pptGeneratorService.generatePpt(request);

        // 验证结果 - 即使没有表格数据，也应该能生成包含标题页的PPT
        assertTrue(response.isSuccess(), "空数据PPT生成应该成功");
        
        System.out.println("空数据测试PPT生成成功:");
        System.out.println("文件路径: " + response.getFilePath());
    }

    @Test
    void testGeneratePptWithNullTitle() {
        TableData tableData = TableData.builder()
                .headers(Arrays.asList("列1", "列2"))
                .rows(Arrays.asList(Arrays.asList("数据1", "数据2")))
                .build();

        PptGenerationRequest request = PptGenerationRequest.builder()
                .title(null)
                .fileName("null-title-test")
                .tables(Arrays.asList(tableData))
                .build();

        // 执行测试
        PptGenerationResponse response = pptGeneratorService.generatePpt(request);

        // 验证结果 - 即使标题为null，也应该能生成PPT
        assertTrue(response.isSuccess(), "空标题PPT生成应该成功");
        
        System.out.println("空标题测试PPT生成成功:");
        System.out.println("文件路径: " + response.getFilePath());
    }
}