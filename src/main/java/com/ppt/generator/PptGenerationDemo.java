package com.ppt.generator;

import com.ppt.generator.model.PptGenerationRequest;
import com.ppt.generator.model.PptGenerationResponse;
import com.ppt.generator.model.TableData;
import com.ppt.generator.service.PptGeneratorService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.util.Arrays;

/**
 * PPT生成演示类
 * 演示如何通过代码直接生成PPT，无需启动Web服务
 * 
 * <AUTHOR> Generator
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PptGenerationDemo implements CommandLineRunner {

    private final PptGeneratorService pptGeneratorService;

    @Override
    public void run(String... args) throws Exception {
        if (args.length > 0 && "demo".equals(args[0])) {
            log.info("=== 开始演示PPT生成功能 ===");
            
            // 生成业务报告PPT
            generateBusinessReportPpt();
            
            // 生成项目进度PPT
            generateProjectProgressPpt();
            
            log.info("=== PPT生成演示完成 ===");
        }
    }

    /**
     * 生成业务报告PPT
     */
    private void generateBusinessReportPpt() {
        log.info("正在生成业务报告PPT...");
        
        // 创建财务数据表格
        TableData financialTable = TableData.builder()
                .title("2024年财务数据汇总")
                .headers(Arrays.asList("科目", "Q1(万元)", "Q2(万元)", "Q3(万元)", "Q4(万元)", "全年总计(万元)"))
                .rows(Arrays.asList(
                    Arrays.asList("营业收入", "2,850", "3,120", "3,450", "3,890", "13,310"),
                    Arrays.asList("营业成本", "1,920", "2,080", "2,280", "2,560", "8,840"),
                    Arrays.asList("毛利润", "930", "1,040", "1,170", "1,330", "4,470"),
                    Arrays.asList("净利润", "450", "520", "580", "690", "2,240")
                ))
                .build();

        // 创建市场分析表格
        TableData marketTable = TableData.builder()
                .title("市场份额分析")
                .headers(Arrays.asList("地区", "用户数量", "市场份额(%)", "增长率(%)", "预期目标(%)"))
                .rows(Arrays.asList(
                    Arrays.asList("华北地区", "125,000", "28.5", "15.2", "30.0"),
                    Arrays.asList("华东地区", "189,000", "35.8", "18.7", "40.0"),
                    Arrays.asList("华南地区", "98,000", "22.1", "12.4", "25.0"),
                    Arrays.asList("西部地区", "56,000", "13.6", "21.3", "20.0")
                ))
                .build();

        PptGenerationRequest request = PptGenerationRequest.builder()
                .title("2024年度业务报告")
                .fileName("business-report-2024")
                .tables(Arrays.asList(financialTable, marketTable))
                .build();

        PptGenerationResponse response = pptGeneratorService.generatePpt(request);
        
        if (response.isSuccess()) {
            log.info("✅ 业务报告PPT生成成功：{}", response.getFileName());
            log.info("   文件路径：{}", response.getFilePath());
            log.info("   文件大小：{} KB", response.getFileSize() / 1024);
            log.info("   生成耗时：{} 毫秒", response.getDuration());
        } else {
            log.error("❌ 业务报告PPT生成失败：{}", response.getMessage());
        }
    }

    /**
     * 生成项目进度PPT
     */
    private void generateProjectProgressPpt() {
        log.info("正在生成项目进度PPT...");
        
        // 创建项目进度表格
        TableData progressTable = TableData.builder()
                .title("项目开发进度跟踪")
                .headers(Arrays.asList("项目名称", "负责人", "开始时间", "预计完成", "当前进度", "状态"))
                .rows(Arrays.asList(
                    Arrays.asList("用户管理系统", "张三", "2024-01-15", "2024-03-15", "95%", "即将完成"),
                    Arrays.asList("订单处理模块", "李四", "2024-02-01", "2024-04-01", "78%", "正常进行"),
                    Arrays.asList("支付集成功能", "王五", "2024-02-15", "2024-04-15", "45%", "正常进行"),
                    Arrays.asList("数据分析平台", "赵六", "2024-03-01", "2024-05-01", "23%", "刚开始"),
                    Arrays.asList("移动端应用", "钱七", "2024-03-15", "2024-06-15", "12%", "需求分析")
                ))
                .build();

        // 创建资源分配表格
        TableData resourceTable = TableData.builder()
                .title("团队资源分配")
                .headers(Arrays.asList("团队", "人员数量", "主要技能", "当前项目", "可用性"))
                .rows(Arrays.asList(
                    Arrays.asList("前端团队", "8", "React/Vue", "用户界面开发", "70%"),
                    Arrays.asList("后端团队", "12", "Java/Python", "API开发", "85%"),
                    Arrays.asList("测试团队", "6", "自动化测试", "质量保证", "60%"),
                    Arrays.asList("设计团队", "4", "UI/UX设计", "界面设计", "40%"),
                    Arrays.asList("运维团队", "3", "DevOps", "系统维护", "90%")
                ))
                .build();

        PptGenerationRequest request = PptGenerationRequest.builder()
                .title("项目进度报告 - 2024年第一季度")
                .fileName("project-progress-q1-2024")
                .tables(Arrays.asList(progressTable, resourceTable))
                .build();

        PptGenerationResponse response = pptGeneratorService.generatePpt(request);
        
        if (response.isSuccess()) {
            log.info("✅ 项目进度PPT生成成功：{}", response.getFileName());
            log.info("   文件路径：{}", response.getFilePath());
            log.info("   文件大小：{} KB", response.getFileSize() / 1024);
            log.info("   生成耗时：{} 毫秒", response.getDuration());
        } else {
            log.error("❌ 项目进度PPT生成失败：{}", response.getMessage());
        }
    }
}