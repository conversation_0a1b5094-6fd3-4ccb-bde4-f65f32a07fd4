package com.ppt.generator.service;

import com.ppt.generator.model.PptGenerationRequest;
import com.ppt.generator.model.PptGenerationResponse;
import com.ppt.generator.model.TableData;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.sl.usermodel.TableCell;
import org.apache.poi.sl.usermodel.TextParagraph.TextAlign;
import org.apache.poi.sl.usermodel.VerticalAlignment;
import org.apache.poi.xslf.usermodel.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.awt.*;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * PPT生成服务类
 * 实现基于Apache POI的PPT自动生成功能
 * 
 * <AUTHOR> Generator
 */
@Slf4j
@Service
public class PptGeneratorService {

    @Value("${ppt.generator.output-path:./generated-ppt/}")
    private String outputPath;

    @Value("${ppt.generator.file-prefix:generated-ppt-}")
    private String filePrefix;

    @Value("${ppt.generator.table.border-color:FFFFFF}")
    private String borderColor;

    @Value("${ppt.generator.table.header-bg-color:4472C4}")
    private String headerBgColor;

    @Value("${ppt.generator.table.header-font-color:FFFFFF}")
    private String headerFontColor;

    @Value("${ppt.generator.table.body-bg-color:F2F2F2}")
    private String bodyBgColor;

    @Value("${ppt.generator.table.body-font-color:000000}")
    private String bodyFontColor;

    /**
     * 生成PPT文档
     */
    public PptGenerationResponse generatePpt(PptGenerationRequest request) {
        long startTime = System.currentTimeMillis();
        
        try {
            log.info("开始生成PPT，标题：{}", request.getTitle());
            
            // 创建PPT演示文稿
            XMLSlideShow ppt = new XMLSlideShow();
            
            // 创建标题页
            createTitleSlide(ppt, request.getTitle());
            
            // 创建表格页面
            if (request.getTables() != null && !request.getTables().isEmpty()) {
                for (int i = 0; i < request.getTables().size(); i++) {
                    TableData tableData = request.getTables().get(i);
                    createTableSlide(ppt, tableData, i + 1);
                }
            }
            
            // 保存文件
            String fileName = generateFileName(request.getFileName());
            String filePath = savePresentation(ppt, fileName);
            
            // 计算文件大小
            File file = new File(filePath);
            long fileSize = file.length();
            long duration = System.currentTimeMillis() - startTime;
            
            log.info("PPT生成成功，文件路径：{}，文件大小：{}字节，耗时：{}毫秒", 
                    filePath, fileSize, duration);
            
            return PptGenerationResponse.success(filePath, fileName, fileSize, duration);
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("PPT生成失败，耗时：{}毫秒", duration, e);
            return PptGenerationResponse.failure("PPT生成失败：" + e.getMessage(), "GENERATION_ERROR");
        }
    }

    /**
     * 创建标题页
     */
    private void createTitleSlide(XMLSlideShow ppt, String title) {
        XSLFSlide titleSlide = ppt.createSlide();
        
        // 创建标题文本框
        XSLFTextBox titleBox = titleSlide.createTextBox();
        titleBox.setAnchor(new Rectangle(50, 100, 600, 100));
        
        XSLFTextParagraph titleParagraph = titleBox.addNewTextParagraph();
        titleParagraph.setTextAlign(TextAlign.CENTER);
        
        XSLFTextRun titleRun = titleParagraph.addNewTextRun();
        titleRun.setText(title != null ? title : "自动生成的PPT报告");
        titleRun.setFontSize(36.0);
        titleRun.setBold(true);
        titleRun.setFontColor(Color.decode("#" + "2F4F4F"));
        
        // 创建副标题
        XSLFTextBox subtitleBox = titleSlide.createTextBox();
        subtitleBox.setAnchor(new Rectangle(50, 220, 600, 50));
        
        XSLFTextParagraph subtitleParagraph = subtitleBox.addNewTextParagraph();
        subtitleParagraph.setTextAlign(TextAlign.CENTER);
        
        XSLFTextRun subtitleRun = subtitleParagraph.addNewTextRun();
        subtitleRun.setText("生成时间：" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        subtitleRun.setFontSize(18.0);
        titleRun.setFontColor(Color.decode("#" + "696969"));
    }

    /**
     * 创建表格页面
     */
    private void createTableSlide(XMLSlideShow ppt, TableData tableData, int slideNumber) {
        XSLFSlide slide = ppt.createSlide();
        
        // 计算表格尺寸和位置
        Dimension pageSize = ppt.getPageSize();
        int tableWidth = (int) (pageSize.width * tableData.getWidthPercent());
        int tableHeight = (int) (pageSize.height * tableData.getHeightPercent());
        int tableX = (int) (pageSize.width * tableData.getX());
        int tableY = (int) (pageSize.height * tableData.getY());
        
        // 添加页面标题
        if (tableData.getTitle() != null && !tableData.getTitle().isEmpty()) {
            XSLFTextBox titleBox = slide.createTextBox();
            titleBox.setAnchor(new Rectangle(tableX, tableY - 80, tableWidth, 60));
            
            XSLFTextParagraph titleParagraph = titleBox.addNewTextParagraph();
            titleParagraph.setTextAlign(TextAlign.CENTER);
            
            XSLFTextRun titleRun = titleParagraph.addNewTextRun();
            titleRun.setText(tableData.getTitle());
            titleRun.setFontSize(24.0);
            titleRun.setBold(true);
            titleRun.setFontColor(Color.decode("#" + "2F4F4F"));
        }
        
        // 创建表格
        int rows = tableData.getTotalRows();
        int cols = tableData.getColumnCount();
        
        if (rows > 0 && cols > 0) {
            XSLFTable table = slide.createTable(rows, cols);
            table.setAnchor(new Rectangle(tableX, tableY, tableWidth, tableHeight));
            
            // 设置表格样式
            styleTable(table, tableData);
        }
        
        log.info("创建表格页面 {}，表格尺寸：{}行 x {}列", slideNumber, rows, cols);
    }

    /**
     * 设置表格样式
     */
    private void styleTable(XSLFTable table, TableData tableData) {
        List<String> headers = tableData.getHeaders();
        List<List<String>> rows = tableData.getRows();
        
        int rowIndex = 0;
        
        // 设置表头
        if (headers != null && !headers.isEmpty()) {
            XSLFTableRow headerRow = table.getRows().get(rowIndex);
            for (int colIndex = 0; colIndex < headers.size(); colIndex++) {
                XSLFTableCell cell = headerRow.getCells().get(colIndex);
                
                // 设置表头内容
                cell.clearText();
                XSLFTextParagraph paragraph = cell.addNewTextParagraph();
                paragraph.setTextAlign(TextAlign.CENTER);
                
                // 设置垂直居中对齐
                cell.setVerticalAlignment(VerticalAlignment.MIDDLE);
                
                XSLFTextRun textRun = paragraph.addNewTextRun();
                textRun.setText(headers.get(colIndex));
                textRun.setFontSize(14.0);
                textRun.setBold(true);
                textRun.setFontColor(Color.decode("#" + headerFontColor));
                
                // 设置表头样式
                cell.setFillColor(Color.decode("#" + headerBgColor));
                cell.setBorderColor(TableCell.BorderEdge.top, Color.decode("#" + borderColor));
                cell.setBorderColor(TableCell.BorderEdge.bottom, Color.decode("#" + borderColor));
                cell.setBorderColor(TableCell.BorderEdge.left, Color.decode("#" + borderColor));
                cell.setBorderColor(TableCell.BorderEdge.right, Color.decode("#" + borderColor));
                cell.setBorderWidth(TableCell.BorderEdge.top, 1.0);
                cell.setBorderWidth(TableCell.BorderEdge.bottom, 1.0);
                cell.setBorderWidth(TableCell.BorderEdge.left, 1.0);
                cell.setBorderWidth(TableCell.BorderEdge.right, 1.0);
            }
            rowIndex++;
        }
        
        // 设置表体
        if (rows != null && !rows.isEmpty()) {
            for (int i = 0; i < rows.size(); i++) {
                List<String> rowData = rows.get(i);
                XSLFTableRow tableRow = table.getRows().get(rowIndex + i);
                
                for (int colIndex = 0; colIndex < rowData.size() && colIndex < table.getNumberOfColumns(); colIndex++) {
                    XSLFTableCell cell = tableRow.getCells().get(colIndex);
                    
                    // 设置表体内容
                    cell.clearText();
                    XSLFTextParagraph paragraph = cell.addNewTextParagraph();
                    paragraph.setTextAlign(TextAlign.CENTER);
                    
//                    // 设置垂直居中对齐
//                    cell.setVerticalAlignment(VerticalAlignment.MIDDLE);
                    
                    XSLFTextRun textRun = paragraph.addNewTextRun();
                    textRun.setText(rowData.get(colIndex));
                    textRun.setFontSize(12.0);
                    textRun.setFontColor(Color.decode("#" + bodyFontColor));
                    
                    // 设置表体样式
                    cell.setFillColor(Color.decode("#" + bodyBgColor));
                    cell.setBorderColor(TableCell.BorderEdge.top, Color.decode("#" + borderColor));
                    cell.setBorderColor(TableCell.BorderEdge.bottom, Color.decode("#" + borderColor));
                    cell.setBorderColor(TableCell.BorderEdge.left, Color.decode("#" + borderColor));
                    cell.setBorderColor(TableCell.BorderEdge.right, Color.decode("#" + borderColor));
                    cell.setBorderWidth(TableCell.BorderEdge.top, 1.0);
                    cell.setBorderWidth(TableCell.BorderEdge.bottom, 1.0);
                    cell.setBorderWidth(TableCell.BorderEdge.left, 1.0);
                    cell.setBorderWidth(TableCell.BorderEdge.right, 1.0);
                }
            }
        }
    }

    /**
     * 生成文件名
     */
    private String generateFileName(String requestFileName) {
        if (requestFileName != null && !requestFileName.trim().isEmpty()) {
            return requestFileName.trim() + ".pptx";
        }
        
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        return filePrefix + timestamp + ".pptx";
    }

    /**
     * 保存演示文稿
     */
    private String savePresentation(XMLSlideShow ppt, String fileName) throws IOException {
        // 确保输出目录存在
        File outputDir = new File(outputPath);
        if (!outputDir.exists()) {
            outputDir.mkdirs();
        }
        
        String filePath = Paths.get(outputPath, fileName).toString();
        
        try (FileOutputStream out = new FileOutputStream(filePath)) {
            ppt.write(out);
        }
        
        ppt.close();
        
        return filePath;
    }
}