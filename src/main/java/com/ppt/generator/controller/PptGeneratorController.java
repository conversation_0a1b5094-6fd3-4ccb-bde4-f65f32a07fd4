package com.ppt.generator.controller;

import com.ppt.generator.model.PptGenerationRequest;
import com.ppt.generator.model.PptGenerationResponse;
import com.ppt.generator.model.TableData;
import com.ppt.generator.service.PptGeneratorService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;

/**
 * PPT生成控制器
 * 提供PPT生成相关的REST API接口
 * 
 * <AUTHOR> Generator
 */
@Slf4j
@RestController
@RequestMapping("/api/ppt")
@RequiredArgsConstructor
public class PptGeneratorController {

    private final PptGeneratorService pptGeneratorService;

    /**
     * 生成PPT接口
     */
    @PostMapping("/generate")
    public ResponseEntity<PptGenerationResponse> generatePpt(@RequestBody PptGenerationRequest request) {
        log.info("收到PPT生成请求，标题：{}", request.getTitle());
        
        try {
            PptGenerationResponse response = pptGeneratorService.generatePpt(request);
            
            if (response.isSuccess()) {
                log.info("PPT生成成功：{}", response.getFileName());
                return ResponseEntity.ok(response);
            } else {
                log.error("PPT生成失败：{}", response.getMessage());
                return ResponseEntity.badRequest().body(response);
            }
            
        } catch (Exception e) {
            log.error("PPT生成过程中发生异常", e);
            PptGenerationResponse errorResponse = PptGenerationResponse.failure(
                "生成PPT时发生系统错误：" + e.getMessage(), 
                "SYSTEM_ERROR"
            );
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 快速生成演示PPT接口（包含预设数据）
     */
    @GetMapping("/generate-demo")
    public ResponseEntity<PptGenerationResponse> generateDemoPpt(
            @RequestParam(defaultValue = "演示报告") String title,
            @RequestParam(defaultValue = "demo-ppt") String fileName) {
        
        log.info("生成演示PPT，标题：{}", title);
        
        // 创建演示数据
        PptGenerationRequest request = createDemoRequest(title, fileName);
        
        return generatePpt(request);
    }

    /**
     * 下载生成的PPT文件
     */
    @GetMapping("/download/{fileName}")
    public ResponseEntity<Resource> downloadPpt(@PathVariable String fileName) {
        try {
            // 构建文件路径
            String filePath = "./generated-ppt/" + fileName;
            File file = new File(filePath);
            
            if (!file.exists()) {
                log.warn("请求下载的文件不存在：{}", fileName);
                return ResponseEntity.notFound().build();
            }
            
            Resource resource = new FileSystemResource(file);
            
            // 设置响应头
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, 
                "attachment; filename*=UTF-8''" + encodedFileName);
            headers.add(HttpHeaders.CONTENT_TYPE, 
                "application/vnd.openxmlformats-officedocument.presentationml.presentation");
            
            log.info("开始下载PPT文件：{}", fileName);
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .contentLength(file.length())
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(resource);
                    
        } catch (Exception e) {
            log.error("下载PPT文件时发生错误：{}", fileName, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取生成的PPT文件列表
     */
    @GetMapping("/files")
    public ResponseEntity<List<String>> listGeneratedFiles() {
        try {
            File outputDir = new File("./generated-ppt/");
            if (!outputDir.exists()) {
                return ResponseEntity.ok(Arrays.asList());
            }
            
            File[] files = outputDir.listFiles((dir, name) -> name.toLowerCase().endsWith(".pptx"));
            List<String> fileNames = files != null ? 
                Arrays.stream(files).map(File::getName).collect(Collectors.toList()) : 
                new ArrayList<>();
            
            log.info("查询到 {} 个PPT文件", fileNames.size());
            
            return ResponseEntity.ok(fileNames);
            
        } catch (Exception e) {
            log.error("获取PPT文件列表时发生错误", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 创建演示请求数据
     */
    private PptGenerationRequest createDemoRequest(String title, String fileName) {
        // 创建销售数据表格
        TableData salesTable = TableData.builder()
                .title("2024年季度销售数据")
                .headers(Arrays.asList("季度", "销售额(万元)", "增长率(%)", "市场份额(%)"))
                .rows(Arrays.asList(
                    Arrays.asList("Q1", "1250.5", "15.2", "23.5"),
                    Arrays.asList("Q2", "1380.8", "18.7", "25.1"),
                    Arrays.asList("Q3", "1425.3", "12.4", "26.8"),
                    Arrays.asList("Q4", "1598.7", "21.3", "28.9")
                ))
                .widthPercent(0.8)
                .heightPercent(0.5)
                .x(0.1)
                .y(0.25)
                .build();

        // 创建产品统计表格
        TableData productTable = TableData.builder()
                .title("产品类别销售统计")
                .headers(Arrays.asList("产品类别", "销售数量", "单价(元)", "总收入(万元)", "占比(%)"))
                .rows(Arrays.asList(
                    Arrays.asList("智能手机", "15,240", "2,899", "4,417.1", "45.2"),
                    Arrays.asList("平板电脑", "8,560", "1,599", "1,369.3", "14.0"),
                    Arrays.asList("笔记本电脑", "5,890", "4,299", "2,532.1", "25.9"),
                    Arrays.asList("智能手表", "12,340", "899", "1,109.4", "11.4"),
                    Arrays.asList("无线耳机", "18,750", "199", "373.1", "3.8")
                ))
                .widthPercent(0.85)
                .heightPercent(0.55)
                .x(0.075)
                .y(0.22)
                .build();

        return PptGenerationRequest.builder()
                .title(title)
                .fileName(fileName)
                .tables(Arrays.asList(salesTable, productTable))
                .useTemplate(true)
                .templateName("default")
                .build();
    }

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public ResponseEntity<String> health() {
        return ResponseEntity.ok("PPT Generator Service is running!");
    }
}