package com.ppt.generator;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

/**
 * PPT生成器SpringBoot应用主启动类
 * 
 * <AUTHOR> Generator
 * @version 1.0.0
 */
@SpringBootApplication
@ComponentScan(basePackages = "com.ppt.generator")
public class PptGeneratorApplication {

    public static void main(String[] args) {
        SpringApplication.run(PptGeneratorApplication.class, args);
        System.out.println("==============================================");
        System.out.println("PPT生成器应用启动成功！");
        System.out.println("访问地址: http://localhost:8080");
        System.out.println("PPT生成接口: http://localhost:8080/api/ppt/generate");
        System.out.println("==============================================");
    }
}