package com.ppt.generator.model;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Builder;

import java.util.List;

/**
 * 表格数据模型
 * 
 * <AUTHOR> Generator
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TableData {
    
    /**
     * 表格标题
     */
    private String title;
    
    /**
     * 表头数据
     */
    private List<String> headers;
    
    /**
     * 表体数据（每一行是一个字符串列表）
     */
    private List<List<String>> rows;
    
    /**
     * 表格宽度（相对于幻灯片宽度的百分比）
     */
    private double widthPercent = 0.8;
    
    /**
     * 表格高度（相对于幻灯片高度的百分比）
     */
    private double heightPercent = 0.6;
    
    /**
     * X坐标位置（相对位置）
     */
    private double x = 0.1;
    
    /**
     * Y坐标位置（相对位置）
     */
    private double y = 0.2;
    
    /**
     * 获取表格行数（包含表头）
     */
    public int getTotalRows() {
        return (headers != null ? 1 : 0) + (rows != null ? rows.size() : 0);
    }
    
    /**
     * 获取表格列数
     */
    public int getColumnCount() {
        if (headers != null && !headers.isEmpty()) {
            return headers.size();
        }
        if (rows != null && !rows.isEmpty()) {
            return rows.get(0).size();
        }
        return 0;
    }
}