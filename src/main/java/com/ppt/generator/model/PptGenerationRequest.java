package com.ppt.generator.model;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Builder;

import java.util.List;

/**
 * PPT生成请求模型
 * 
 * <AUTHOR> Generator
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PptGenerationRequest {
    
    /**
     * PPT标题
     */
    private String title;
    
    /**
     * PPT文件名（不包含扩展名）
     */
    private String fileName;
    
    /**
     * 表格数据列表
     */
    private List<TableData> tables;
    
    /**
     * 是否使用模板
     */
    private boolean useTemplate = true;
    
    /**
     * 模板名称
     */
    private String templateName = "default";
    
    /**
     * 额外的文本内容
     */
    private List<String> additionalTexts;
}