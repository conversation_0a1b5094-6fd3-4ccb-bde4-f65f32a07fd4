package com.ppt.generator.model;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Builder;

/**
 * PPT生成响应模型
 * 
 * <AUTHOR> Generator
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PptGenerationResponse {
    
    /**
     * 是否成功
     */
    private boolean success;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 生成的文件路径
     */
    private String filePath;
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 文件大小（字节）
     */
    private long fileSize;
    
    /**
     * 生成耗时（毫秒）
     */
    private long duration;
    
    /**
     * 错误代码（如果失败）
     */
    private String errorCode;
    
    /**
     * 创建成功响应
     */
    public static PptGenerationResponse success(String filePath, String fileName, long fileSize, long duration) {
        return PptGenerationResponse.builder()
                .success(true)
                .message("PPT生成成功")
                .filePath(filePath)
                .fileName(fileName)
                .fileSize(fileSize)
                .duration(duration)
                .build();
    }
    
    /**
     * 创建失败响应
     */
    public static PptGenerationResponse failure(String message, String errorCode) {
        return PptGenerationResponse.builder()
                .success(false)
                .message(message)
                .errorCode(errorCode)
                .build();
    }
}