server:
  port: 8080
  servlet:
    context-path: /

spring:
  application:
    name: ppt-generator
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB

# PPT生成器配置
ppt:
  generator:
    # 输出目录
    output-path: "./generated-ppt/"
    # 模板路径  
    template-path: "templates/"
    # 默认文件名前缀
    file-prefix: "generated-ppt-"
    # 表格样式配置
    table:
      # 表格边框颜色（白色）
      border-color: "FFFFFF"
      # 表头背景色
      header-bg-color: "4472C4"
      # 表头字体颜色
      header-font-color: "FFFFFF"
      # 表体背景色
      body-bg-color: "F2F2F2"
      # 表体字体颜色
      body-font-color: "000000"

# 日志配置
logging:
  level:
    com.ppt.generator: DEBUG
    org.apache.poi: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"