@echo off
chcp 65001 >nul
echo ==========================================
echo PPT自动生成器 - 运行指南
echo ==========================================

echo.
echo 1. 编译项目：
echo    mvn clean compile

echo.
echo 2. 运行项目：
echo    mvn spring-boot:run

echo.
echo 3. 生成演示PPT（代码方式）：
echo    mvn spring-boot:run -Dspring-boot.run.arguments=demo

echo.
echo 4. 访问Web接口：
echo    健康检查：http://localhost:8080/api/ppt/health
echo    生成演示PPT：http://localhost:8080/api/ppt/generate-demo
echo    文件列表：http://localhost:8080/api/ppt/files

echo.
echo 5. 使用curl测试接口：
echo    curl "http://localhost:8080/api/ppt/generate-demo?title=测试报告&fileName=test"

echo.
echo 6. 下载生成的PPT：
echo    访问：http://localhost:8080/api/ppt/download/{fileName}

echo.
echo ==========================================
echo 项目特性：
echo • 基于SpringBoot + Apache POI
echo • 支持自动生成包含表格的PPT
echo • 表格边框自动设置为白色
echo • 完整的REST API接口
echo • 支持自定义数据和模板
echo ==========================================

echo.
echo 按任意键开始编译项目...
pause >nul

echo 开始编译项目...
mvn clean compile

echo.
echo 编译完成！现在可以运行项目：
echo mvn spring-boot:run
echo.
pause