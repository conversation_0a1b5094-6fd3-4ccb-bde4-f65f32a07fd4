# PPT自动生成器

## 项目简介

这是一个基于SpringBoot和Apache POI的自动化PPT生成工具，能够根据预设模板和数据自动生成包含表格的PowerPoint演示文稿。

## 主要功能

- **一键生成PPT**：通过执行代码即可生成符合预设模板规范的PPT文档
- **表格支持**：支持生成包含完整表头和表体结构的表格
- **样式定制**：表格内部边框设置为白色，支持自定义颜色方案
- **REST API**：提供完整的REST API接口，支持远程调用
- **异常处理**：包含完整的异常处理机制，确保系统稳定性

## 技术栈

- **SpringBoot 3.2.0**：应用框架
- **Apache POI 5.2.5**：Office文档处理
- **Lombok**：简化代码
- **JUnit 5**：单元测试
- **Maven**：项目管理

## 项目结构

```
ppt-generator/
├── src/
│   ├── main/
│   │   ├── java/com/ppt/generator/
│   │   │   ├── PptGeneratorApplication.java      # 主启动类
│   │   │   ├── controller/
│   │   │   │   └── PptGeneratorController.java   # REST API控制器
│   │   │   ├── service/
│   │   │   │   └── PptGeneratorService.java      # PPT生成服务
│   │   │   └── model/
│   │   │       ├── TableData.java                # 表格数据模型
│   │   │       ├── PptGenerationRequest.java     # 请求模型
│   │   │       └── PptGenerationResponse.java    # 响应模型
│   │   └── resources/
│   │       └── application.yml                   # 配置文件
│   └── test/                                      # 测试代码
├── generated-ppt/                                # PPT输出目录
└── pom.xml                                       # Maven配置
```

## 快速开始

### 1. 环境要求

- JDK 17 或更高版本
- Maven 3.6 或更高版本

### 2. 构建项目

```bash
mvn clean compile
```

### 3. 运行应用

```bash
mvn spring-boot:run
```

### 4. 访问应用

- 应用地址：http://localhost:8080
- 健康检查：http://localhost:8080/api/ppt/health

## API 接口

### 1. 生成演示PPT

```http
GET /api/ppt/generate-demo?title=演示报告&fileName=demo-ppt
```

### 2. 自定义数据生成PPT

```http
POST /api/ppt/generate
Content-Type: application/json

{
  "title": "销售报告",
  "fileName": "sales-report",
  "tables": [
    {
      "title": "季度销售数据",
      "headers": ["季度", "销售额(万元)", "增长率(%)"],
      "rows": [
        ["Q1", "1250.5", "15.2"],
        ["Q2", "1380.8", "18.7"],
        ["Q3", "1425.3", "12.4"],
        ["Q4", "1598.7", "21.3"]
      ]
    }
  ]
}
```

### 3. 下载PPT文件

```http
GET /api/ppt/download/{fileName}
```

### 4. 查看生成的文件列表

```http
GET /api/ppt/files
```

## 表格样式配置

在 `application.yml` 中可以自定义表格样式：

```yaml
ppt:
  generator:
    table:
      border-color: "FFFFFF"        # 表格边框颜色（白色）
      header-bg-color: "4472C4"     # 表头背景色
      header-font-color: "FFFFFF"   # 表头字体颜色
      body-bg-color: "F2F2F2"       # 表体背景色
      body-font-color: "000000"     # 表体字体颜色
```

## 测试

### 运行单元测试

```bash
mvn test
```

### 测试覆盖的场景

- 单表格PPT生成
- 多表格PPT生成
- 空数据处理
- API接口测试
- 异常处理测试

## 一键生成示例

项目启动后，可以通过以下方式快速生成PPT：

1. **通过API调用**：
   ```bash
   curl "http://localhost:8080/api/ppt/generate-demo?title=测试报告"
   ```

2. **通过代码调用**：
   ```java
   @Autowired
   private PptGeneratorService pptGeneratorService;
   
   public void generatePpt() {
       // 创建表格数据
       TableData tableData = TableData.builder()
           .title("销售数据")
           .headers(Arrays.asList("产品", "销量", "收入"))
           .rows(Arrays.asList(
               Arrays.asList("产品A", "100", "10000"),
               Arrays.asList("产品B", "150", "15000")
           ))
           .build();
       
       // 创建生成请求
       PptGenerationRequest request = PptGenerationRequest.builder()
           .title("销售报告")
           .fileName("sales-report")
           .tables(Arrays.asList(tableData))
           .build();
       
       // 生成PPT
       PptGenerationResponse response = pptGeneratorService.generatePpt(request);
       System.out.println("PPT生成结果：" + response.getMessage());
   }
   ```

## 输出文件

生成的PPT文件会保存在 `./generated-ppt/` 目录下，文件特点：

- **格式**：.pptx
- **内容**：包含标题页和表格页
- **样式**：表格边框为白色，支持自定义配色
- **命名**：支持自定义文件名或自动生成时间戳文件名

## 注意事项

1. 确保有足够的磁盘空间存储生成的PPT文件
2. 表格数据量过大时可能影响生成性能
3. 建议定期清理 `generated-ppt` 目录中的旧文件
4. 如需修改默认样式，请参考配置文件说明

## 许可证

本项目采用 MIT 许可证。